part of '../home_screen.dart';

/// Stateful widget for file list display with integrated operations
/// Consolidates file operations and API calls for better maintainability
class HomeFileListSection extends StatefulWidget {
  final String searchQuery;
  final Function(DocumentModel)? onDocumentTap;
  final Function(DocumentModel)? onDocumentMenu;
  final VoidCallback? onFilterTap;

  const HomeFileListSection({
    super.key,
    required this.searchQuery,
    this.onDocumentTap,
    this.onDocumentMenu,
    this.onFilterTap,
  });

  /// Factory constructor for home screen file list
  factory HomeFileListSection.forHomeScreen({
    required String searchQuery,
    required Function(DocumentModel) onDocumentTap,
    required Function(DocumentModel) onDocumentMenu,
    required VoidCallback onFilterTap,
  }) {
    return HomeFileListSection(
      searchQuery: searchQuery,
      onDocumentTap: onDocumentTap,
      onDocumentMenu: onDocumentMenu,
      onFilterTap: onFilterTap,
    );
  }

  @override
  State<HomeFileListSection> createState() => _HomeFileListSectionState();
}

class _HomeFileListSectionState extends State<HomeFileListSection> {
  @override
  Widget build(BuildContext context) {
    return Consumer<DocumentProvider>(
      builder: (context, documentProvider, child) {
        // Get recent files (last 30 days for home screen)
        final recentDocuments = documentProvider.getRecentFiles(days: 30);

        // Apply search filter
        final searchFilteredDocuments = _applySearchFilter(
          recentDocuments,
          widget.searchQuery,
        );

        return FileTableWidget.forHomeScreen(
          documents: searchFilteredDocuments,
          onDocumentTap: widget.onDocumentTap,
          onFilter: widget.onFilterTap,
        );
      },
    );
  }

  /// Apply search filter to documents
  List<DocumentModel> _applySearchFilter(
    List<DocumentModel> documents,
    String searchQuery,
  ) {
    if (searchQuery.isEmpty) return documents;

    final query = searchQuery.toLowerCase();
    return documents.where((document) {
      return document.fileName.toLowerCase().contains(query) ||
          document.fileType.toLowerCase().contains(query) ||
          (document.metadata.description.toLowerCase().contains(query));
    }).toList();
  }
}
