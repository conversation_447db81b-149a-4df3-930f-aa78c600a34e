part of '../home_screen.dart';

/// Stateless widget for displaying dashboard statistics
/// Uses composition pattern with individual stat cards
class HomeDashboardStats extends StatelessWidget {
  const HomeDashboardStats({super.key});

  /// Factory constructor for admin-only stats
  factory HomeDashboardStats.forAdmin() {
    return const HomeDashboardStats();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<DocumentProvider, UserProvider, CategoryProvider>(
      builder:
          (context, documentProvider, userProvider, categoryProvider, child) {
            // Calculate statistics
            final totalDocuments = documentProvider.documents.length;
            final recentDocuments = documentProvider
                .getRecentFiles(days: 7)
                .length;
            final totalUsers = userProvider.users.length;
            final totalCategories = categoryProvider.categories.length;

            return Container(
              margin: const EdgeInsets.fromLTRB(16, 2, 16, 2),
              child: Row(
                children: [
                  Expanded(
                    child: _StatCard(
                      title: 'Total Files',
                      value: totalDocuments.toString(),
                      icon: Icons.description,
                      color: AppColors.primary,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _StatCard(
                      title: 'Recent',
                      value: recentDocuments.toString(),
                      icon: Icons.access_time,
                      color: AppColors.success,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _StatCard(
                      title: 'Users',
                      value: totalUsers.toString(),
                      icon: Icons.people,
                      color: AppColors.warning,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _StatCard(
                      title: 'Categories',
                      value: totalCategories.toString(),
                      icon: Icons.folder,
                      color: AppColors.info,
                    ),
                  ),
                ],
              ),
            );
          },
    );
  }
}

/// Individual stat card component
/// Follows single responsibility principle
class _StatCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  const _StatCard({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w700,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
