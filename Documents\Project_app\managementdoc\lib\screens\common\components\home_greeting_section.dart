part of '../home_screen.dart';

/// Stateless widget for displaying greeting section on home screen
/// Follows composition pattern for better maintainability
class HomeGreetingSection extends StatelessWidget {
  final AuthProvider authProvider;
  final GreetingSet currentGreeting;

  const HomeGreetingSection({
    super.key,
    required this.authProvider,
    required this.currentGreeting,
  });

  /// Factory constructor for creating greeting section with current user
  factory HomeGreetingSection.withCurrentUser({
    required BuildContext context,
    required GreetingSet greeting,
  }) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    return HomeGreetingSection(
      authProvider: authProvider,
      currentGreeting: greeting,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        // Anda bisa mengubah warna background di sini
        color: AppColors.surface,
        // Anda bisa mengubah border radius di sini
        borderRadius: BorderRadius.circular(16),
        // Anda bisa mengubah shadow di sini
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // User Avatar
          _buildUserAvatar(),
          const SizedBox(width: 16),
          // Greeting Content
          Expanded(child: _buildGreetingContent()),
        ],
      ),
    );
  }

  Widget _buildUserAvatar() {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.2),
          width: 2,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(23),
        child:
            authProvider.currentUser?.profileImage != null &&
                authProvider.currentUser!.profileImage!.isNotEmpty
            ? Image.network(
                authProvider.currentUser!.profileImage!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) =>
                    _buildDefaultAvatar(),
              )
            : _buildDefaultAvatar(),
      ),
    );
  }

  Widget _buildDefaultAvatar() {
    return Icon(Icons.person, color: AppColors.shadow, size: 24);
  }

  Widget _buildGreetingContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          currentGreeting.personalGreeting,
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          currentGreeting.mainGreeting,
          style: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        if (authProvider.isAdmin) ...[
          const SizedBox(height: 2),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          ),
        ],
      ],
    );
  }
}
